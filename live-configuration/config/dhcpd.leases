# The format of this file is documented in the dhcpd.leases(5) manual page.
# This lease file was written by isc-dhcp-4.1-ESV-R15-P1

# authoring-byte-order entry is generated, DO NOT DELETE
authoring-byte-order big-endian;

lease 192.168.10.13 {
  starts 3 2025/09/24 15:06:48;
  ends 4 2025/09/25 15:06:48;
#shared-network: WIFI_USERS
  tstp 4 2025/09/25 15:06:48;
  cltt 3 2025/09/24 15:06:48;
  binding state free;
  hardware ethernet 54:b7:bd:c6:a4:ad;
  uid "\001T\267\275\306\244\255";
}
lease 192.168.10.10 {
  starts 3 2025/09/24 16:03:54;
  ends 4 2025/09/25 16:03:54;
#shared-network: WIFI_USERS
  tstp 4 2025/09/25 16:03:54;
  cltt 3 2025/09/24 16:03:54;
  binding state free;
  hardware ethernet c0:48:e6:2c:f3:b8;
  uid "\001\300H\346,\363\270";
}
lease 192.168.10.16 {
  starts 4 2025/09/25 14:35:27;
  ends 5 2025/09/26 14:35:27;
#shared-network: WIFI_USERS
  tstp 5 2025/09/26 14:35:27;
  cltt 4 2025/09/25 14:35:27;
  binding state free;
  hardware ethernet 8e:ca:39:90:ab:54;
  uid "\001\216\3129\220\253T";
}
lease ************* {
  starts 5 2025/09/26 12:23:59;
  ends 6 2025/09/27 12:23:59;
#shared-network: WIFI_USERS
  cltt 5 2025/09/26 12:23:59;
  binding state active;
  next binding state free;
  hardware ethernet 08:8e:90:0e:11:c2;
  uid "\001\010\216\220\016\021\302";
  client-hostname "5CG21505W6";
}
lease ************* {
  starts 6 2025/09/27 07:52:57;
  ends 0 2025/09/28 07:52:57;
#shared-network: WIFI_USERS
  cltt 6 2025/09/27 07:52:57;
  binding state active;
  next binding state free;
  hardware ethernet 82:62:3e:19:2b:39;
  uid "\001\202b>\031+9";
  client-hostname "OnePlus-Nord-2T-5G";
}
lease ************* {
  starts 6 2025/09/27 08:09:40;
  ends 0 2025/09/28 08:09:40;
#shared-network: WIFI_USERS
  cltt 6 2025/09/27 08:09:40;
  binding state active;
  next binding state free;
  hardware ethernet 52:ce:45:9a:a3:1d;
  uid "\001R\316E\232\243\035";
}
lease ************* {
  starts 6 2025/09/27 08:26:59;
  ends 0 2025/09/28 08:26:59;
#shared-network: WIFI_USERS
  cltt 6 2025/09/27 08:26:59;
  binding state active;
  next binding state free;
  hardware ethernet 30:e3:a4:ac:d5:a5;
  uid "\0010\343\244\254\325\245";
  client-hostname "mdw-u7-p135f";
}
lease ************* {
  starts 6 2025/09/27 09:01:38;
  ends 0 2025/09/28 09:01:38;
#shared-network: WIFI_USERS
  cltt 6 2025/09/27 09:01:38;
  binding state active;
  next binding state free;
  hardware ethernet aa:92:68:47:d7:75;
  uid "\001\252\222hG\327u";
  client-hostname "OnePlus-Nord-CE-3-Lite-5G";
}
lease ************ {
  starts 3 2025/09/24 14:03:44;
  ends 4 2025/09/25 14:03:44;
#shared-network: MEDIA
  tstp 4 2025/09/25 14:03:44;
  cltt 3 2025/09/24 14:03:44;
  binding state free;
  hardware ethernet ae:61:d1:4d:d3:c7;
  uid "\001\256a\321M\323\307";
}
lease ************ {
  starts 4 2025/09/25 14:27:27;
  ends 5 2025/09/26 14:27:27;
#shared-network: MEDIA
  tstp 5 2025/09/26 14:27:27;
  cltt 4 2025/09/25 14:27:27;
  binding state free;
  hardware ethernet b8:2c:a0:bd:16:72;
  uid "\001\270,\240\275\026r";
}
lease ************ {
  starts 3 2025/09/24 11:32:44;
  ends 3 2025/09/24 13:50:13;
#shared-network: LAN1
  tstp 3 2025/09/24 13:50:13;
  cltt 3 2025/09/24 11:32:44;
  binding state free;
  hardware ethernet 70:a7:41:c6:e7:f8;
  uid "\001p\247A\306\347\370";
}
lease ************ {
  starts 3 2025/09/24 12:35:17;
  ends 3 2025/09/24 13:54:17;
#shared-network: LAN1
  tstp 3 2025/09/24 13:54:17;
  cltt 3 2025/09/24 12:35:17;
  binding state free;
  hardware ethernet 94:2a:6f:4c:fb:58;
  uid "\001\224*oL\373X";
}
lease 192.168.1.40 {
  starts 3 2025/09/24 12:35:15;
  ends 3 2025/09/24 13:58:40;
#shared-network: LAN1
  tstp 3 2025/09/24 13:58:40;
  cltt 3 2025/09/24 12:35:15;
  binding state free;
  hardware ethernet 68:d7:9a:d6:25:46;
  uid "\001h\327\232\326%F";
}
lease 192.168.1.199 {
  starts 4 2025/09/25 11:58:04;
  ends 4 2025/09/25 12:00:19;
#shared-network: LAN1
  tstp 4 2025/09/25 12:00:19;
  cltt 4 2025/09/25 11:58:04;
  binding state free;
  hardware ethernet c0:48:e6:2c:f3:b8;
  uid "\001\300H\346,\363\270";
}
lease 192.168.1.43 {
  starts 3 2025/09/24 13:50:26;
  ends 4 2025/09/25 13:50:26;
#shared-network: LAN1
  tstp 4 2025/09/25 13:50:26;
  cltt 3 2025/09/24 13:50:26;
  binding state free;
  hardware ethernet 88:4a:ea:30:0a:0d;
  uid "\001\210J\3520\012\015";
}
lease 192.168.1.42 {
  starts 3 2025/09/24 13:50:39;
  ends 4 2025/09/25 13:50:39;
#shared-network: LAN1
  tstp 4 2025/09/25 13:50:39;
  cltt 3 2025/09/24 13:50:39;
  binding state free;
  hardware ethernet b8:2c:a0:bd:16:72;
  uid "\001\270,\240\275\026r";
}
lease 192.168.1.140 {
  starts 3 2025/09/24 13:58:52;
  ends 4 2025/09/25 13:58:52;
#shared-network: LAN1
  tstp 4 2025/09/25 13:58:52;
  cltt 3 2025/09/24 13:58:52;
  binding state free;
  hardware ethernet ae:61:d1:4d:d3:c7;
  uid "\001\256a\321M\323\307";
}
lease 192.168.1.201 {
  starts 4 2025/09/25 07:07:40;
  ends 5 2025/09/26 07:07:40;
#shared-network: LAN1
  tstp 5 2025/09/26 07:07:40;
  cltt 4 2025/09/25 07:07:40;
  binding state free;
  hardware ethernet ac:b4:80:d8:e0:92;
  uid "\001\254\264\200\330\340\222";
}
lease 192.168.1.44 {
  starts 4 2025/09/25 07:16:32;
  ends 5 2025/09/26 07:16:32;
#shared-network: LAN1
  tstp 5 2025/09/26 07:16:32;
  cltt 4 2025/09/25 07:16:32;
  binding state free;
  hardware ethernet 54:b7:bd:c6:a4:ad;
  uid "\001T\267\275\306\244\255";
}
lease 192.168.1.197 {
  starts 4 2025/09/25 07:37:56;
  ends 5 2025/09/26 07:37:56;
#shared-network: LAN1
  tstp 5 2025/09/26 07:37:56;
  cltt 4 2025/09/25 07:37:56;
  binding state free;
  hardware ethernet 52:ce:45:9a:a3:1d;
  uid "\001R\316E\232\243\035";
}
lease 192.168.1.109 {
  starts 4 2025/09/25 07:57:12;
  ends 5 2025/09/26 07:57:12;
#shared-network: LAN1
  tstp 5 2025/09/26 07:57:12;
  cltt 4 2025/09/25 07:57:12;
  binding state free;
  hardware ethernet c8:d3:ff:a0:ca:de;
  uid "\001\310\323\377\240\312\336";
}
lease 192.168.1.131 {
  starts 4 2025/09/25 08:06:55;
  ends 5 2025/09/26 08:06:55;
#shared-network: LAN1
  tstp 5 2025/09/26 08:06:55;
  cltt 4 2025/09/25 08:06:55;
  binding state free;
  hardware ethernet 82:62:3e:19:2b:39;
  uid "\001\202b>\031+9";
}
lease 192.168.1.200 {
  starts 4 2025/09/25 13:23:12;
  ends 5 2025/09/26 13:23:12;
#shared-network: LAN1
  tstp 5 2025/09/26 13:23:12;
  cltt 4 2025/09/25 13:23:12;
  binding state free;
  hardware ethernet aa:92:68:47:d7:75;
  uid "\001\252\222hG\327u";
}
lease ************ {
  starts 4 2025/09/25 14:36:24;
  ends 5 2025/09/26 14:36:24;
#shared-network: LAN1
  tstp 5 2025/09/26 14:36:24;
  cltt 4 2025/09/25 14:36:24;
  binding state free;
  hardware ethernet 30:e3:a4:ac:d5:a5;
  uid "\0010\343\244\254\325\245";
}
lease ************* {
  starts 5 2025/09/26 18:42:31;
  ends 6 2025/09/27 18:42:31;
#shared-network: LAN1
  cltt 5 2025/09/26 18:42:31;
  binding state active;
  next binding state free;
  hardware ethernet 48:65:ee:19:05:6c;
  uid "\001He\356\031\005l";
  client-hostname "5CG21505W6";
}
lease ************* {
  starts 5 2025/09/26 22:32:34;
  ends 6 2025/09/27 22:32:34;
#shared-network: LAN1
  cltt 5 2025/09/26 22:32:34;
  binding state active;
  next binding state free;
  hardware ethernet 6c:63:f8:4d:45:cc;
  client-hostname "USW-Flex-Mini";
}
lease ************ {
  starts 6 2025/09/27 00:01:15;
  ends 0 2025/09/28 00:01:15;
#shared-network: LAN1
  cltt 6 2025/09/27 00:01:15;
  binding state active;
  next binding state free;
  hardware ethernet 14:ae:85:70:57:fb;
  uid "\001\024\256\205pW\373";
}
lease ************ {
  starts 6 2025/09/27 08:58:59;
  ends 6 2025/09/27 09:00:59;
#shared-network: IOT_TRUSTED
  cltt 6 2025/09/27 08:58:59;
  binding state free;
  hardware ethernet 2c:d1:c6:f5:1d:bf;
  client-hostname "VWINFOTAINMENT";
}
lease ************ {
  starts 6 2025/09/27 08:07:00;
  ends 0 2025/09/28 08:07:00;
#shared-network: IOT_TRUSTED
  cltt 6 2025/09/27 08:07:00;
  binding state active;
  next binding state free;
  hardware ethernet ae:61:d1:4d:d3:c7;
  uid "\001\256a\321M\323\307";
}
lease *********** {
  starts 3 2025/09/24 14:58:56;
  ends 3 2025/09/24 15:00:56;
#shared-network: GUEST
  tstp 3 2025/09/24 15:00:56;
  cltt 3 2025/09/24 14:58:56;
  binding state free;
  hardware ethernet 30:e3:a4:ac:d5:a5;
  uid "\0010\343\244\254\325\245";
  client-hostname "mdw-u7-p135f";
}
lease *********** {
  starts 3 2025/09/24 18:35:15;
  ends 4 2025/09/25 18:35:15;
#shared-network: GUEST
  tstp 4 2025/09/25 18:35:15;
  cltt 3 2025/09/24 18:35:15;
  binding state free;
  hardware ethernet a6:7f:4b:fc:3e:0b;
  uid "\001\246\177K\374>\013";
}
lease *********** {
  starts 6 2025/09/27 08:21:19;
  ends 0 2025/09/28 08:21:19;
#shared-network: GUEST
  cltt 6 2025/09/27 08:21:19;
  binding state active;
  next binding state free;
  hardware ethernet 70:a7:41:c6:e7:f8;
  uid "\001p\247A\306\347\370";
}
lease *********** {
  starts 6 2025/09/27 08:21:35;
  ends 0 2025/09/28 08:21:35;
#shared-network: GUEST
  cltt 6 2025/09/27 08:21:35;
  binding state active;
  next binding state free;
  hardware ethernet 68:d7:9a:d6:25:46;
  uid "\001h\327\232\326%F";
}
lease *********** {
  starts 5 2025/09/26 22:32:41;
  ends 6 2025/09/27 22:32:41;
#shared-network: GUEST
  cltt 5 2025/09/26 22:32:41;
  binding state abandoned;
  next binding state free;
}
lease *********** {
  starts 6 2025/09/27 08:21:15;
  ends 0 2025/09/28 08:21:15;
#shared-network: GUEST
  tstp 0 2025/09/28 08:21:15;
  cltt 6 2025/09/27 08:21:15;
  binding state abandoned;
  next binding state free;
}
server-duid "\000\001\000\0010f\223Y$ZLPc(";

lease ************* {
  starts 6 2025/09/27 09:01:39;
  ends 0 2025/09/28 09:01:39;
#shared-network: WIFI_USERS
  cltt 6 2025/09/27 09:01:39;
  binding state active;
  next binding state free;
  hardware ethernet aa:92:68:47:d7:75;
  uid "\001\252\222hG\327u";
  client-hostname "OnePlus-Nord-CE-3-Lite-5G";
}
lease ************* {
  starts 6 2025/09/27 09:03:35;
  ends 0 2025/09/28 09:03:35;
#shared-network: WIFI_USERS
  cltt 6 2025/09/27 09:03:35;
  binding state active;
  next binding state free;
  hardware ethernet aa:92:68:47:d7:75;
  uid "\001\252\222hG\327u";
  client-hostname "OnePlus-Nord-CE-3-Lite-5G";
}
