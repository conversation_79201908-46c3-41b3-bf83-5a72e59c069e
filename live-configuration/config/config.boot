firewall {
    all-ping enable
    broadcast-ping disable
    group {
        address-group UNIFI_DEVICES {
            address ***********
            address ***********
            address ***********
            address ***********
            description "UniFi Infrastructure Devices"
        }
        network-group ALL_INTERNAL {
            description "All Internal Networks"
            network ***********/24
            network ***********/24
            network ***********/24
            network ***********/24
            network ***********/24
            network ************/24
            network **********/24
        }
        network-group GUEST_NETWORK {
            description "Guest Network - WiFi Only"
            network **********/24
        }
        network-group IOT_TRUSTED {
            description "IoT Trusted - WiFi Only"
            network ***********/24
        }
        network-group IOT_UNTRUSTED {
            description "IoT Untrusted - WiFi Only"
            network ***********/24
        }
        network-group MEDIA_NETWORKS {
            description "Media Network - Wired + WiFi"
            network ***********/24
        }
        network-group MUSICCAST_CLIENTS {
            description "Networks allowed to access MusicCast"
            network ***********/24
            network ************/24
            network ***********/24
        }
        network-group TRUSTED_NETWORKS {
            description "All Trusted Networks"
            network ***********/24
            network ***********/24
            network ***********/24
            network ************/24
        }
        network-group USER_NETWORKS {
            description "User Networks - WiFi and Media"
            network ************/24
            network ***********/24
        }
        network-group VPN_NETWORK {
            description "WireGuard VPN network"
            network ***********/24
        }
        network-group WIFI_USERS {
            description "Primary WiFi Users"
            network ************/24
        }
        network-group WIRED_DEVICES {
            description "All Wired Devices - Trusted"
            network ***********/24
        }
    }
    ipv6-name WANv6_IN {
        default-action drop
        description "WAN inbound traffic forwarded to LAN"
        enable-default-log
        rule 10 {
            action accept
            description "Allow established/related sessions"
            state {
                established enable
                related enable
            }
        }
        rule 20 {
            action drop
            description "Drop invalid state"
            state {
                invalid enable
            }
        }
    }
    ipv6-name WANv6_LOCAL {
        default-action drop
        description "WAN inbound traffic to the router"
        enable-default-log
        rule 10 {
            action accept
            description "Allow established/related sessions"
            state {
                established enable
                related enable
            }
        }
        rule 20 {
            action drop
            description "Drop invalid state"
            state {
                invalid enable
            }
        }
        rule 30 {
            action accept
            description "Allow IPv6 icmp"
            protocol ipv6-icmp
        }
        rule 40 {
            action accept
            description "allow dhcpv6"
            destination {
                port 546
            }
            protocol udp
            source {
                port 547
            }
        }
    }
    ipv6-receive-redirects disable
    ipv6-src-route disable
    ip-src-route disable
    log-martians enable
    name GUEST_IN {
        default-action drop
        description "Guest network - internet only"
        rule 1 {
            action accept
            description "Allow DNS queries"
            destination {
                port 53
            }
            log disable
            protocol tcp_udp
        }
        rule 2 {
            action accept
            description "Allow established/related"
            log disable
            protocol all
            state {
                established enable
                invalid disable
                new disable
                related enable
            }
        }
        rule 3 {
            action drop
            description "Block ALL internal network access"
            log enable
            protocol all
        }
        rule 4 {
            action drop
            description "Block EdgeRouter management"
            destination {
                address **********
                port 22,80,443
            }
            log enable
            protocol tcp
        }
        rule 5 {
            action drop
            description "Block access to UniFi devices"
            destination {
                group {
                    address-group UNIFI_DEVICES
                }
            }
            log enable
            protocol all
        }
        rule 6 {
            action drop
            description "Block ALL internal network access"
            destination {
                group {
                    network-group ALL_INTERNAL
                }
            }
            log enable
            protocol all
        }
    }
    name IOT_TRUSTED_IN {
        default-action drop
        description "IoT trusted devices access control"
        rule 10 {
            action accept
            description "Allow IoT to Home Assistant"
            destination {
                address *************
                port 8123
            }
            log disable
            protocol tcp
        }
        rule 20 {
            action accept
            description "Allow IoT to Zigbee controller"
            destination {
                address *************
                port 8080
            }
            log disable
            protocol tcp
        }
        rule 30 {
            action accept
            description "Allow MQTT communication"
            destination {
                address *************
                port 1883,8883
            }
            log disable
            protocol tcp
        }
        rule 40 {
            action accept
            description "Allow DNS to internal server only"
            destination {
                address *************
                port 53
            }
            log disable
            protocol tcp_udp
        }
        rule 50 {
            action accept
            description "Allow NTP to router only"
            destination {
                address ***********
                port 123
            }
            log disable
            protocol udp
        }
        rule 60 {
            action accept
            description "Allow outgoing traffic"
            destination {
                port 80,443,5671
            }
            log enable
            protocol tcp
            source {
            }
            state {
                established disable
                invalid disable
                new enable
                related disable
            }
        }
        rule 70 {
            action accept
            description "Allow DNS queries"
            destination {
                port 53
            }
            log enable
            protocol tcp_udp
            source {
            }
            state {
                established disable
                invalid disable
                new enable
                related disable
            }
        }
        rule 80 {
            action accept
            description "Allow NTP time sync"
            destination {
                port 123
            }
            log enable
            protocol udp
            source {
            }
            state {
                established disable
                invalid disable
                new enable
                related disable
            }
        }
        rule 90 {
            action accept
            description "Allow established/related"
            log disable
            protocol all
            state {
                established enable
                invalid disable
                new disable
                related enable
            }
        }
        rule 100 {
            action drop
            description "Block access to other networks"
            log enable
            protocol all
        }
    }
    name IOT_UNTRUSTED_IN {
        default-action drop
        description "IoT untrusted - internet only"
        rule 1 {
            action accept
            description "Allow DNS queries"
            destination {
                port 53
            }
            log disable
            protocol tcp_udp
        }
        rule 2 {
            action accept
            description "Allow NTP for time sync"
            destination {
                port 123
            }
            log disable
            protocol udp
        }
        rule 3 {
            action accept
            description "Allow established/related"
            log disable
            protocol all
            state {
                established enable
                invalid disable
                new disable
                related enable
            }
        }
        rule 4 {
            action drop
            description "Block ALL internal network access"
            destination {
                group {
                    network-group ALL_INTERNAL
                }
            }
            log enable
            protocol all
        }
        rule 5 {
            action drop
            description "Block access to UniFi devices"
            destination {
                group {
                    address-group UNIFI_DEVICES
                }
            }
            log enable
            protocol all
        }
    }
    name MEDIA_IN {
        default-action drop
        description "Media network access control"
        rule 20 {
            action accept
            description "Allow DNS queries"
            destination {
                port 53
            }
            log disable
            protocol tcp_udp
        }
        rule 80 {
            action accept
            description "Allow established/related"
            log disable
            protocol all
            state {
                established enable
                invalid disable
                new disable
                related enable
            }
        }
        rule 90 {
            action accept
            description "Allow outbound internet access"
            log disable
            protocol all
            state {
                established disable
                invalid disable
                new enable
                related disable
            }
        }
        rule 100 {
            action drop
            description "Block access to other networks"
            log enable
            protocol all
        }
    }
    name VPN_IN {
        default-action drop
        description "VPN clients to internal networks"
        rule 1 {
            action accept
            description "Allow VPN to all internal networks"
            destination {
                group {
                    network-group TRUSTED_NETWORKS
                }
            }
            log disable
            protocol all
            source {
                group {
                    network-group VPN_NETWORK
                }
            }
        }
    }
    name WAN_IN {
        default-action drop
        description "WAN to internal"
        rule 10 {
            action accept
            description "Allow WireGuard VPN"
            destination {
                port 51820
            }
            log disable
            protocol udp
        }
        rule 20 {
            action accept
            description "Allow established/related"
            state {
                established enable
                related enable
            }
        }
        rule 30 {
            action drop
            description "Block UniFi device access from WAN"
            destination {
                group {
                    address-group UNIFI_DEVICES
                }
            }
            log enable
            protocol tcp_udp
        }
        rule 50 {
            action drop
            description "Drop invalid state"
            state {
                invalid enable
            }
        }
    }
    name WAN_LOCAL {
        default-action drop
        description "WAN to router"
        rule 10 {
            action accept
            description "Allow WireGuard VPN"
            destination {
                port 51820
            }
            log disable
            protocol udp
        }
        rule 20 {
            action accept
            description "Allow established/related"
            state {
                established enable
                related enable
            }
        }
        rule 30 {
            action drop
            description "Drop invalid state"
            state {
                invalid enable
            }
        }
    }
    name WIFI_USERS_IN {
        default-action drop
        description "WiFi users to internal"
        rule 10 {
            action accept
            description "Allow WiFi users to core services"
            destination {
                group {
                    network-group WIRED_DEVICES
                }
            }
            log disable
            protocol tcp
        }
        rule 20 {
            action accept
            description "Allow DNS queries"
            destination {
                port 53
            }
            log disable
            protocol tcp_udp
        }
        rule 30 {
            action accept
            description "Allow outbound internet access"
            log disable
            protocol all
            state {
                established disable
                invalid disable
                new enable
                related disable
            }
        }
        rule 40 {
            action accept
            description "Allow established/related"
            log disable
            protocol all
            state {
                established enable
                invalid disable
                new disable
                related enable
            }
        }
        rule 50 {
            action drop
            description "Block direct access to UniFi devices"
            destination {
                group {
                    address-group UNIFI_DEVICES
                }
                port 22,80,443,8080,8443
            }
            log enable
            protocol tcp
        }
    }
    receive-redirects disable
    send-redirects enable
    source-validation disable
    syn-cookies enable
}
interfaces {
    ethernet eth0 {
        address dhcp
        description Internet
        duplex auto
        firewall {
            in {
                name WAN_IN
            }
            local {
                name WAN_LOCAL
            }
        }
        speed auto
    }
    ethernet eth1 {
        address ***********/24
        description "LAN - Core Infrastructure & Trunk to USW Flex 2.5G"
        duplex auto
        speed auto
        vif 2 {
            address **********/24
            description "Guest Network - WiFi Only"
            firewall {
                in {
                    name GUEST_IN
                }
            }
            mtu 1500
        }
        vif 3 {
            address ***********/24
            description "Media Network - DLNA Optimized"
            firewall {
                in {
                    name MEDIA_IN
                }
            }
            mtu 1500
        }
        vif 4 {
            address ***********/24
            description "IoT Trusted - WiFi Only"
            firewall {
                in {
                    name IOT_TRUSTED_IN
                }
            }
            mtu 1500
        }
        vif 6 {
            address ***********/24
            description "IoT Untrusted - WiFi Only"
            firewall {
                in {
                    name IOT_UNTRUSTED_IN
                }
            }
            mtu 1500
        }
        vif 10 {
            address ************/24
            description "Primary WiFi Users"
            firewall {
                in {
                    name WIFI_USERS_IN
                }
            }
            mtu 1500
        }
    }
    ethernet eth2 {
        address ***********/24
        description "Local 2"
        disable
        duplex auto
        speed auto
    }
    ethernet eth3 {
        disable
        duplex auto
        speed auto
    }
    loopback lo {
    }
    wireguard wg0 {
        address ***********/24
        description "WireGuard VPN Server"
        firewall {
            in {
                name VPN_IN
            }
        }
        listen-port 51820
        mtu 1420
        peer joHYCXSHua4VZdJpzKYjhNMQ8htdL9VJlmc/7IIv+z4= {
            allowed-ips ***********/32
            description "oneplus 5T"
            endpoint wg.mdewaele.freeddns.org:51820
            preshared-key RueOMo+Ea4Z3vG4iSOVf1DojfAz+7k0Pys7zTLF4Mcw=
        }
        private-key CK0sPOcXncfz82E0UsfGb+W3QrNNSnG5qJBZRCFWg1w=
        route-allowed-ips true
    }
}
protocols {
    igmp-proxy {
        interface eth1 {
            alt-subnet ***********/24
            role upstream
            threshold 1
        }
        interface eth1.3 {
            alt-subnet ***********/24
            role downstream
            threshold 1
        }
        interface eth1.10 {
            alt-subnet ************/24
            role downstream
            threshold 1
        }
    }
    static {
        interface-route ***********/24 {
            next-hop-interface wg0 {
            }
        }
    }
}
service {
    dhcp-server {
        disabled false
        hostfile-update disable
        shared-network-name GUEST {
            authoritative disable
            subnet **********/24 {
                default-router **********
                dns-server *******
                dns-server *******
                lease 86400
                start **********0 {
                    stop **********00
                }
            }
        }
        shared-network-name IOT_TRUSTED {
            authoritative disable
            subnet ***********/24 {
                default-router ***********
                dns-server *************
                lease 86400
                start ***********0 {
                    stop ************
                }
                static-mapping Brother_DCP1610W {
                    ip-address ***********1
                    mac-address 74:29:af:2e:74:e8
                }
                static-mapping Samsung_TV {
                    ip-address ***********0
                    mac-address 00:a0:de:db:55:08
                }
                static-mapping Tstat_T6 {
                    ip-address ***********2
                    mac-address b8:2c:a0:bd:16:72
                }
            }
        }
        shared-network-name IOT_UNTRUSTED {
            authoritative disable
            subnet ***********/24 {
                default-router ***********
                dns-server *******
                lease 86400
                start ***********0 {
                    stop ***********00
                }
            }
        }
        shared-network-name LAN1 {
            authoritative enable
            subnet ***********/24 {
                default-router ***********
                dns-server *************
                dns-server *******
                lease 86400
                start ***********8 {
                    stop ***********43
                }
                static-mapping TL-SG105PE {
                    ip-address ***********
                    mac-address 60:32:B1:5D:54:4B
                }
                static-mapping kitchen-speaker {
                    ip-address ***********02
                    mac-address 00:A0:DE:F6:B1:37
                }
                static-mapping livingroom-speaker {
                    ip-address ***********04
                    mac-address 00:A0:DE:DB:55:08
                }
                static-mapping synology-nas-lag {
                    ip-address *************
                    mac-address 90:09:d0:7b:ad:25
                }
                static-mapping unifi-ac-pro {
                    ip-address ***********
                    mac-address 68:d7:9a:d6:25:46
                }
                static-mapping unifi-u6-lite {
                    ip-address ***********
                    mac-address 70:a7:41:c6:e7:f8
                }
                static-mapping usw-flex {
                    ip-address ***********
                    mac-address 94:2a:6f:4c:fb:58
                }
            }
        }
        shared-network-name LAN2 {
            authoritative enable
            disable
            subnet ***********/24 {
                default-router ***********
                dns-server ***********
                lease 86400
                start ************ {
                    stop *************
                }
            }
        }
        shared-network-name MEDIA {
            authoritative disable
            subnet ***********/24 {
                default-router ***********
                dns-server *************
                dns-server *******
                lease 86400
                start ************ {
                    stop *************
                }
                static-mapping Samsung {
                    ip-address ************
                    mac-address c0:48:e6:2c:f3:b8
                }
                static-mapping bedroom-speaker {
                    ip-address ************
                    mac-address 88:4a:ea:30:0a:0d
                }
                static-mapping bureau-speaker {
                    ip-address ************
                    mac-address 54:b7:bd:c6:a4:ad
                }
            }
        }
        shared-network-name WIFI_USERS {
            authoritative disable
            subnet ************/24 {
                default-router ************
                dns-server *************
                dns-server *******
                lease 86400
                start ************0 {
                    stop **************
                }
            }
        }
        static-arp disable
        use-dnsmasq disable
    }
    dns {
        dynamic {
            interface eth0 {
                service dyndns {
                    host-name mdewaele.freeddns.org
                    login mdewaele
                    password J9Jr1xTvfDYlEo8Y
                    server api.dynu.com
                }
                web checkip.dynu.com
            }
        }
        forwarding {
            cache-size 10000
            listen-on eth1
            listen-on eth2
        }
    }
    gui {
        http-port 80
        https-port 443
        older-ciphers enable
    }
    mdns {
        repeater {
            interface eth1
            interface eth1.3
            interface eth1.10
        }
    }
    nat {
        rule 5010 {
            description "masquerade for WAN"
            log disable
            outbound-interface eth0
            protocol all
            type masquerade
        }
        rule 5011 {
            description "Core/wired devices to internet"
            log disable
            outbound-interface eth0
            protocol all
            source {
                address ***********/24
            }
            type masquerade
        }
        rule 5012 {
            description "WiFi users to internet"
            log disable
            outbound-interface eth0
            protocol all
            source {
                address ************/24
            }
            type masquerade
        }
        rule 5013 {
            description "Media network to internet"
            log disable
            outbound-interface eth0
            source {
                address ***********/24
            }
            type masquerade
        }
        rule 5016 {
            description "Guest network to internet"
            log disable
            outbound-interface eth0
            source {
                address **********/24
            }
            type masquerade
        }
        rule 5017 {
            description "VPN clients to internet"
            log disable
            outbound-interface eth0
            source {
                address ***********/24
            }
            type masquerade
        }
        rule 5018 {
            description "T6 internet access"
            log disable
            outbound-interface eth0
            protocol tcp
            source {
                address ***********2
            }
            type masquerade
        }
    }
    ssh {
        port 22
        protocol-version v2
    }
    unms {
        disable
    }
}
system {
    analytics-handler {
        send-analytics-report false
    }
    crash-handler {
        send-crash-report false
    }
    host-name EdgeRouter-4
    login {
        user mdewaele {
            authentication {
                encrypted-password $5$h.dUmHn7wwqrRpZR$gNihgXsK4MqVsK7c8zuBNVWmeuN05IkdBGYm8DU8bw/
            }
            level admin
        }
    }
    ntp {
        server 0.ubnt.pool.ntp.org {
        }
        server 1.ubnt.pool.ntp.org {
        }
        server 2.ubnt.pool.ntp.org {
        }
        server 3.ubnt.pool.ntp.org {
        }
    }
    static-host-mapping {
        host-name unifi-controller {
            inet *************
        }
    }
    syslog {
        global {
            facility all {
                level notice
            }
            facility protocols {
                level debug
            }
        }
    }
    time-zone UTC
}


/* Warning: Do not remove the following line. */
/* === vyatta-config-version: "config-management@1:conntrack@1:cron@1:dhcp-relay@1:dhcp-server@4:firewall@5:ipsec@5:nat@3:qos@1:quagga@2:suspend@1:system@5:ubnt-l2tp@1:ubnt-pptp@1:ubnt-udapi-server@1:ubnt-unms@2:ubnt-util@1:vrrp@1:vyatta-netflow@1:webgui@1:webproxy@1:zone-policy@1" === */
/* Release version: v3.0.0.5842788.250718.1103 */
